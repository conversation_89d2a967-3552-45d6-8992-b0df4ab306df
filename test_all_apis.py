#!/usr/bin/env python3
"""
Comprehensive test script for all API fixes:
1. Website search API - YouTube links and Arabic language
2. Company search API - Arabic country names and stock codes
3. Author search API - Image URLs and professions format
4. Category search API - Removed unwanted fields
5. Book search API - Author info structure
"""

import requests
import json
import sys

BASE_URL = "http://127.0.0.1:8000/api/books"

def test_website_search():
    """Test website search API fixes"""
    print("\n🌐 Testing Website Search API")
    print("=" * 50)
    
    # Test Netflix in English
    response = requests.post(f"{BASE_URL}/website-search/", json={
        "website_name": "Netflix",
        "language": "en"
    })
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Netflix (EN) - Success")
        
        # Check YouTube link
        youtube = data.get('social_media', {}).get('youtube', '')
        if youtube:
            print(f"✅ YouTube link found: {youtube}")
        else:
            print("❌ YouTube link missing")
            
        # Check other social media
        social_media = data.get('social_media', {})
        for platform, link in social_media.items():
            if link:
                print(f"  {platform}: {link}")
    else:
        print(f"❌ Netflix (EN) failed: {response.status_code}")
    
    # Test Netflix in Arabic
    response = requests.post(f"{BASE_URL}/website-search/", json={
        "website_name": "Netflix",
        "language": "ar"
    })
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Netflix (AR) - Success")
        print(f"  Name: {data.get('name', '')}")
        print(f"  Country: {data.get('country', '')}")
    else:
        print(f"❌ Netflix (AR) failed: {response.status_code}")

def test_company_search():
    """Test company search API fixes"""
    print("\n🏢 Testing Company Search API")
    print("=" * 50)
    
    # Test TCS stock code
    response = requests.post(f"{BASE_URL}/company-search/", json={
        "company_name": "TCS.NS",
        "language": "en"
    })
    
    if response.status_code == 200:
        data = response.json()
        print("✅ TCS.NS (EN) - Success")
        print(f"  Name: {data.get('name', '')}")
        print(f"  Code: {data.get('code', '')}")
        print(f"  Country: {data.get('country_origin', '')}")
    else:
        print(f"❌ TCS.NS (EN) failed: {response.status_code}")
    
    # Test TCS in Arabic
    response = requests.post(f"{BASE_URL}/company-search/", json={
        "company_name": "TCS",
        "language": "ar"
    })
    
    if response.status_code == 200:
        data = response.json()
        print("✅ TCS (AR) - Success")
        print(f"  Name: {data.get('name', '')}")
        print(f"  Country: {data.get('country_origin', '')}")
    else:
        print(f"❌ TCS (AR) failed: {response.status_code}")

def test_author_search():
    """Test author search API fixes"""
    print("\n📚 Testing Author Search API")
    print("=" * 50)
    
    # Test Stephen King
    response = requests.post(f"{BASE_URL}/author-search/", json={
        "author_name": "Stephen King",
        "language": "en"
    })
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Stephen King (EN) - Success")
        print(f"  Image: {data.get('author_image', 'None')}")
        
        # Check professions format
        professions = data.get('professions', [])
        if professions and isinstance(professions[0], dict):
            print("✅ Professions in correct object format:")
            for prof in professions:
                print(f"    {prof}")
        else:
            print("❌ Professions not in object format")
    else:
        print(f"❌ Stephen King (EN) failed: {response.status_code}")
    
    # Test in Arabic
    response = requests.post(f"{BASE_URL}/author-search/", json={
        "author_name": "جين أوستن",
        "language": "ar"
    })
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Jane Austen (AR) - Success")
        print(f"  Name: {data.get('name', '')}")
        
        # Check if content is in Arabic
        bio = data.get('bio', '')
        if any('\u0600' <= char <= '\u06FF' for char in bio):
            print("✅ Biography in Arabic")
        else:
            print("❌ Biography not in Arabic")
    else:
        print(f"❌ Jane Austen (AR) failed: {response.status_code}")

def test_category_search():
    """Test category search API fixes"""
    print("\n📂 Testing Category Search API")
    print("=" * 50)
    
    response = requests.post(f"{BASE_URL}/category-search/", json={
        "category_name": "Entertainment",
        "language": "en"
    })
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Entertainment (EN) - Success")
        
        # Check removed fields
        removed_fields = ['subcategories', 'related_fields', 'industry_size', 'notable_companies']
        for field in removed_fields:
            if field in data:
                print(f"❌ Field '{field}' should be removed but still present")
            else:
                print(f"✅ Field '{field}' correctly removed")
                
        # Check remaining fields
        required_fields = ['name', 'icon', 'wikilink', 'description']
        for field in required_fields:
            if field in data:
                print(f"✅ Field '{field}' present")
            else:
                print(f"❌ Field '{field}' missing")
    else:
        print(f"❌ Entertainment (EN) failed: {response.status_code}")

def test_book_search():
    """Test book search API fixes"""
    print("\n📖 Testing Book Search API")
    print("=" * 50)
    
    response = requests.post(f"{BASE_URL}/ai-book-search-no-db/", json={
        "book_name": "Pride and Prejudice",
        "language": "en",
        "max_results": 2
    })
    
    if response.status_code == 200:
        data = response.json()
        results = data.get('results', [])
        
        if results:
            print("✅ Book search - Success")
            result = results[0]
            
            # Check author field removal
            if 'author' in result:
                print("❌ 'author' field should be removed")
            else:
                print("✅ 'author' field correctly removed")
            
            # Check author_info presence
            if 'author_info' in result:
                print("✅ 'author_info' field present")
            else:
                print("❌ 'author_info' field missing")
            
            # Check categories structure
            categories = result.get('categories', [])
            if categories:
                print(f"✅ Categories found: {len(categories)} categories")
                for i, cat in enumerate(categories):
                    print(f"  Category {i+1}: {cat.get('name', 'Unknown')}")
            else:
                print("❌ No categories found")
        else:
            print("❌ No results returned")
    else:
        print(f"❌ Book search failed: {response.status_code}")

def main():
    """Run all tests"""
    print("🚀 Starting Comprehensive API Tests")
    print("=" * 60)
    
    try:
        test_website_search()
        test_company_search()
        test_author_search()
        test_category_search()
        test_book_search()
        
        print("\n✅ All tests completed!")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure Django server is running on port 8000.")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
